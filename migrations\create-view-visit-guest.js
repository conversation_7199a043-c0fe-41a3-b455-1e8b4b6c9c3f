'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(` 
            CREATE VIEW view_visit_guest AS
            select 
            g.guest_id as guest_id,
            f.facility_id as facility_id,
            v.visit_id as visit_id,
            v.host_id as host_id,
            f.name as facility_name, 
            concat(i.first_name, ' ', i.last_name) as host_name, 
            concat(g.first_name,' ', g.last_name) as guest_name,
            gv.check_in_time as check_in_time,
            gv.check_out_time as check_out_time,
            gv.guest_status as guest_status
            from guest_visit gv 
            join visit v on gv.visit_id = v.visit_id
            join "guest" g on gv.guest_id = g.guest_id
            join "identity" i on v.host_id = i.identity_id
            join facility f on v.facility_id = f.facility_id
    `);
    },

    down: async (queryInterface, Sequelize) => {
        return queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS view_visit_guest;
    `);
    },
};
