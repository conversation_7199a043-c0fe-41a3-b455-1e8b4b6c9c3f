module.exports = (sequelize, DataTypes) => {
  const VisitGuestView = sequelize.define(
    "VisitGuestView",
    {
      guest_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        allowNull: false,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      visit_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      host_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      facility_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      host_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      guest_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      check_in_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      check_out_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      guest_status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      }
    },
    {
      tableName: "view_visit_guest",
      timestamps: false,
      underscored: true,
    }
  );

  return VisitGuestView;
};
